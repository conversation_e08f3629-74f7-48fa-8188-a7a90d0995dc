import { createClient } from '@supabase/supabase-js';
import { VisaFormData } from './types';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export const saveFormData = async (
  formData: VisaFormData,
  agentId: string,
  step: number,
  uploadedFiles: { [key: string]: string } = {}
) => {
  try {
    const { data, error } = await supabase
      .from('visa_applications')
      .upsert({
        agent_id: agentId,
        step_status: step,
        form_data: formData,
        uploaded_files: uploadedFiles,
        updated_at: new Date(),
      })
      .select();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error saving form data:', error);
    return { data: null, error };
  }
};

export const getFormData = async (agentId: string) => {
  try {
    const { data, error } = await supabase
      .from('visa_applications')
      .select('*')
      .eq('agent_id', agentId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error getting form data:', error);
    return { data: null, error };
  }
};

export const searchApplicationsByPassport = async (passportNumber: string) => {
  try {
    if (!passportNumber) {
      return { data: null, error: new Error('Passport number is required') };
    }

    console.log('Searching for application with passport:', passportNumber);
    
    // We need to search inside the JSONB form_data for passportNumber
    const { data, error } = await supabase
      .from('visa_applications')
      .select('*')
      .filter('form_data->passportNumber', 'eq', passportNumber)
      .order('created_at', { ascending: false })
      .limit(1); // Get the most recent application

    if (error) {
      console.error('Supabase error searching applications:', error);
      throw error;
    }

    console.log('Search results:', data);
    
    // Check if we got a valid result
    if (data && data.length > 0) {
      return { data: data[0], error: null };
    } else {
      return { data: null, error: null }; // Not found but no error
    }
  } catch (error) {
    console.error('Error searching for applications:', error);
    return { data: null, error };
  }
};

export const markWhatsappRedirected = async (agentId: string) => {
  try {
    const { error } = await supabase
      .from('visa_applications')
      .update({ whatsapp_redirected: true })
      .eq('agent_id', agentId);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error marking WhatsApp redirected:', error);
    return { error };
  }
}; 