import { createClient } from '@supabase/supabase-js';
import { VisaFormData } from './types';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create Supabase client with explicit configuration
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  },
  global: {
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    }
  }
});

export const saveFormData = async (
  formData: VisaFormData,
  agentId: string,
  step: number,
  uploadedFiles: { [key: string]: string } = {},
  retryCount = 0
): Promise<{ data: any; error: Error | null }> => {
  const maxRetries = 3;

  try {
    console.log(`Saving form data for agent ${agentId}, step ${step}, attempt ${retryCount + 1}`);

    const { data, error } = await supabase
      .from('visa_applications')
      .upsert({
        agent_id: agentId,
        step_status: step,
        form_data: formData,
        uploaded_files: uploadedFiles,
        updated_at: new Date(),
      })
      .select();

    if (error) throw error;

    console.log('Form data saved successfully');
    return { data, error: null };
  } catch (error) {
    console.error(`Error saving form data (attempt ${retryCount + 1}):`, error);

    // Retry on network errors or temporary failures
    if (retryCount < maxRetries &&
        (error instanceof Error &&
         (error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('503') ||
          error.message.includes('502')))) {

      console.log(`Retrying save operation in ${(retryCount + 1) * 1000}ms...`);
      await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
      return saveFormData(formData, agentId, step, uploadedFiles, retryCount + 1);
    }

    return { data: null, error: error instanceof Error ? error : new Error(String(error)) };
  }
};

export const getFormData = async (agentId: string, retryCount = 0): Promise<{ data: any; error: Error | null }> => {
  const maxRetries = 3;

  try {
    console.log(`Getting form data for agent ${agentId}, attempt ${retryCount + 1}`);

    // Validate inputs
    if (!agentId || agentId.trim() === '') {
      throw new Error('Agent ID is required');
    }

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Supabase configuration is missing');
    }

    const { data, error } = await supabase
      .from('visa_applications')
      .select('*')
      .eq('agent_id', agentId)
      .maybeSingle(); // Use maybeSingle instead of single to avoid errors when no data found

    if (error) {
      console.error('Supabase error:', error);
      throw error;
    }

    console.log('Form data retrieved successfully:', data ? 'found' : 'not found');
    return { data, error: null };
  } catch (error) {
    console.error(`Error getting form data (attempt ${retryCount + 1}):`, error);

    // Retry on network errors or 406 errors
    if (retryCount < maxRetries &&
        (error instanceof Error &&
         (error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('406') ||
          error.message.includes('503') ||
          error.message.includes('502')))) {

      console.log(`Retrying get operation in ${(retryCount + 1) * 1000}ms...`);
      await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
      return getFormData(agentId, retryCount + 1);
    }

    return { data: null, error: error instanceof Error ? error : new Error(String(error)) };
  }
};

export const searchApplicationsByPassport = async (passportNumber: string) => {
  try {
    if (!passportNumber) {
      return { data: null, error: new Error('Passport number is required') };
    }

    console.log('Searching for application with passport:', passportNumber);

    // We need to search inside the JSONB form_data for passportNumber
    // Using proper PostgREST syntax for JSONB queries
    const { data, error } = await supabase
      .from('visa_applications')
      .select('*')
      .eq('form_data->>passportNumber', passportNumber)
      .order('created_at', { ascending: false })
      .limit(1); // Get the most recent application

    if (error) {
      console.error('Supabase error searching applications:', error);
      throw error;
    }

    console.log('Search results:', data);

    // Check if we got a valid result
    if (data && data.length > 0) {
      return { data: data[0], error: null };
    } else {
      return { data: null, error: null }; // Not found but no error
    }
  } catch (error) {
    console.error('Error searching for applications:', error);
    return { data: null, error };
  }
};

export const markWhatsappRedirected = async (agentId: string) => {
  try {
    const { error } = await supabase
      .from('visa_applications')
      .update({ whatsapp_redirected: true })
      .eq('agent_id', agentId);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error marking WhatsApp redirected:', error);
    return { error };
  }
};

// Test Supabase connection
export const testSupabaseConnection = async () => {
  try {
    console.log('Testing Supabase connection...');
    console.log('Supabase URL:', supabaseUrl);
    console.log('Supabase Key (first 10 chars):', supabaseAnonKey.substring(0, 10) + '...');

    // Validate configuration first
    if (!supabaseUrl || !supabaseAnonKey) {
      const configError = new Error('Supabase configuration is missing');
      console.error('Configuration error:', configError);
      return { success: false, error: configError };
    }

    // Try a simple query to test the connection
    // If the table doesn't exist, we'll get a different error than connection issues
    const { data, error } = await supabase
      .from('visa_applications')
      .select('id')
      .limit(1);

    if (error) {
      // Check if it's a table not found error (which means connection works)
      if (error.code === 'PGRST106' || error.message.includes('does not exist')) {
        console.log('Supabase connection successful, but table needs to be created');
        return { success: true, data: null, needsTableCreation: true };
      }

      console.error('Supabase connection test failed:', error);
      return { success: false, error };
    }

    console.log('Supabase connection test successful');
    return { success: true, data };
  } catch (error) {
    console.error('Supabase connection test error:', error);
    return { success: false, error };
  }
};
