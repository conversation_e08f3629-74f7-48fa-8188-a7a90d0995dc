import React, { useState } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import { step2Schema } from '../utils/validations';
import FileUpload from './FileUpload';
import { ExtractedDocumentData } from '../utils/ocr';
import { Step2Data } from '../utils/types';

interface Step2Props {
  initialValues: Step2Data;
  onSubmit: (values: Step2Data) => void;
  uploadedFiles: { [key: string]: string };
  setUploadedFiles: (files: { [key: string]: string }) => void;
}

const Step2_DocumentUpload: React.FC<Step2Props> = ({
  initialValues,
  onSubmit,
  uploadedFiles,
  setUploadedFiles,
}) => {
  const [formValues, setFormValues] = useState(initialValues);

  const handleDocumentExtract = (documentType: string, data: ExtractedDocumentData) => {
    setFormValues((prev) => ({
      ...prev,
      ...data,
    }));
    
    // This is where we'd handle the actual file storage, typically in a cloud service
    // For now, we're just tracking that a file was uploaded for this document type
    setUploadedFiles({
      ...uploadedFiles,
      [documentType]: 'uploaded',
    });
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Загрузите документы</h2>
      <p className="text-gray-600 mb-6">
        Пожалуйста, загрузите скан или фото вашего паспорта и удостоверения личности. 
        Мы автоматически извлечем данные, которые вы сможете проверить и отредактировать.
      </p>

      <div className="mb-6">
        <FileUpload
          label="Загрузите скан/фото паспорта"
          name="passport"
          onExtract={(data) => handleDocumentExtract('passport', data)}
        />
        
        <FileUpload
          label="Загрузите скан/фото удостоверения личности"
          name="idCard"
          onExtract={(data) => handleDocumentExtract('idCard', data)}
        />
      </div>

      <div className="mt-8">
        <h3 className="text-lg font-medium text-gray-800 mb-4">
          Проверьте и отредактируйте данные
        </h3>

        <Formik
          initialValues={formValues}
          validationSchema={step2Schema}
          onSubmit={onSubmit}
          enableReinitialize
        >
          {({ isValid: _isValid }) => (
            <Form>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="mb-4">
                  <label htmlFor="surname" className="block text-gray-700 font-medium mb-2">
                    Фамилия (Surname)
                  </label>
                  <Field
                    type="text"
                    id="surname"
                    name="surname"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="surname"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="name" className="block text-gray-700 font-medium mb-2">
                    Имя (Name)
                  </label>
                  <Field
                    type="text"
                    id="name"
                    name="name"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="name"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="dateOfBirth" className="block text-gray-700 font-medium mb-2">
                    Дата рождения
                  </label>
                  <Field
                    type="date"
                    id="dateOfBirth"
                    name="dateOfBirth"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="dateOfBirth"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="citizenship" className="block text-gray-700 font-medium mb-2">
                    Гражданство
                  </label>
                  <Field
                    type="text"
                    id="citizenship"
                    name="citizenship"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="citizenship"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="passportNumber" className="block text-gray-700 font-medium mb-2">
                    Номер паспорта
                  </label>
                  <Field
                    type="text"
                    id="passportNumber"
                    name="passportNumber"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="passportNumber"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="passportIssueDate" className="block text-gray-700 font-medium mb-2">
                    Дата выдачи паспорта
                  </label>
                  <Field
                    type="date"
                    id="passportIssueDate"
                    name="passportIssueDate"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="passportIssueDate"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="passportExpiryDate" className="block text-gray-700 font-medium mb-2">
                    Дата окончания паспорта
                  </label>
                  <Field
                    type="date"
                    id="passportExpiryDate"
                    name="passportExpiryDate"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="passportExpiryDate"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="iin" className="block text-gray-700 font-medium mb-2">
                    ИИН
                  </label>
                  <Field
                    type="text"
                    id="iin"
                    name="iin"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="iin"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="idNumber" className="block text-gray-700 font-medium mb-2">
                    Номер удостоверения личности
                  </label>
                  <Field
                    type="text"
                    id="idNumber"
                    name="idNumber"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="idNumber"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default Step2_DocumentUpload; 