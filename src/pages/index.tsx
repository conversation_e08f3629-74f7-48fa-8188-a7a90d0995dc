import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

import StepWrapper from '../components/StepWrapper';
import Step1_Welcome from '../components/Step1_Welcome';
import Step2_DocumentUpload from '../components/Step2_DocumentUpload';
import Step3_PersonalInfo from '../components/Step3_PersonalInfo';
import Step4_TravelPurpose from '../components/Step4_TravelPurpose';
import Step5_VisaHistory from '../components/Step5_VisaHistory';
import Step6_ContactInfo from '../components/Step6_ContactInfo';
import Step7_FamilyInfo from '../components/Step7_FamilyInfo';
import Step8_EducationWork from '../components/Step8_EducationWork';
import Step9_TravelHistory from '../components/Step9_TravelHistory';

import { saveFormData, getFormData, markWhatsappRedirected, testSupabaseConnection } from '../utils/supabase';
import {
  VisaFormData,
  Step1Data,
  Step2Data,
  Step3Data,
  Step4Data,
  Step5Data,
  Step6Data,
  Step7Data,
  Step8Data,
  Step9Data
} from '../utils/types';

const totalSteps = 9;

export default function Home() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [agentId, setAgentId] = useState('agentDefault000');
  const [formData, setFormData] = useState<VisaFormData>({
    // Step 1
    country: '',

    // Step 2
    surname: '',
    name: '',
    dateOfBirth: '',
    citizenship: '',
    passportNumber: '',
    passportIssueDate: '',
    passportExpiryDate: '',
    iin: '',
    idNumber: '',

    // Step 3
    fullNameCyrillic: '',
    hasOtherNames: false,
    otherNames: '',
    gender: '',
    maritalStatus: '',
    cityOfBirth: '',
    countryOfBirth: '',
    hasOtherCitizenship: false,
    otherCitizenship: '',
    isPermanentResidentOtherCountry: false,
    permanentResidenceCountry: '',
    nationality: '',
    hasSSN: false,
    ssn: '',
    hasTaxpayerId: false,
    taxpayerId: '',
  });

  const [uploadedFiles, setUploadedFiles] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');

  // Get agent_id from URL query parameter
  useEffect(() => {
    const urlAgentId = router.query.agent_id;
    if (typeof urlAgentId === 'string') {
      setAgentId(urlAgentId);
    }
  }, [router.query.agent_id]);

  // Load existing data when agent_id is available
  useEffect(() => {
    const loadExistingData = async () => {
      try {
        setIsLoading(true);

        // Test Supabase connection first
        console.log('Testing Supabase connection...');
        const connectionTest = await testSupabaseConnection();
        if (!connectionTest.success) {
          console.error('Supabase connection failed:', connectionTest.error);
          setErrorMessage('Ошибка подключения к базе данных. Проверьте настройки Supabase.');
          return;
        }

        const { data, error } = await getFormData(agentId);

        if (error) throw error;

        if (data) {
          setFormData((prevFormData) => data.form_data as VisaFormData || prevFormData);
          setStep(data.step_status || 1);
          setUploadedFiles(data.uploaded_files || {});
        }
      } catch (error) {
        console.error('Failed to load existing data:', error);
        setErrorMessage('Не удалось загрузить данные. Пожалуйста, попробуйте обновить страницу.');
      } finally {
        setIsLoading(false);
      }
    };

    if (agentId) {
      loadExistingData();
    }
  }, [agentId]);

  const handleNext = async () => {
    if (step === totalSteps) {
      try {
        // Redirect to WhatsApp
        const phoneNumber = '+77064172408';
        await markWhatsappRedirected(agentId);
        window.location.href = `https://wa.me/${phoneNumber}?text=Я заполнил(а) анкету. Мой ID: ${agentId}`;
      } catch (error) {
        console.error('Failed to redirect to WhatsApp:', error);
        setErrorMessage('Не удалось перейти в WhatsApp. Пожалуйста, попробуйте еще раз.');
      }
    } else {
      setStep((prevStep) => prevStep + 1);
    }
  };

  const handlePrev = () => {
    setStep((prevStep) => Math.max(1, prevStep - 1));
  };

  // Type-safe step submit handlers
  const handleStep1Submit = async (stepData: Step1Data) => {
    try {
      const updatedFormData = {
        ...formData,
        ...stepData,
      };

      setFormData(updatedFormData);
      // Save with the next step number to ensure correct progress tracking
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep2Submit = async (stepData: Step2Data) => {
    try {
      const updatedFormData = {
        ...formData,
        ...stepData,
      };

      setFormData(updatedFormData);
      // Save with the next step number to ensure correct progress tracking
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep3Submit = async (stepData: Step3Data) => {
    try {
      const updatedFormData = {
        ...formData,
        ...stepData,
      };

      setFormData(updatedFormData);
      // Save with the next step number to ensure correct progress tracking
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep4Submit = async (stepData: Step4Data) => {
    try {
      const updatedFormData = {
        ...formData,
        ...stepData,
      };

      setFormData(updatedFormData);
      // Save with the next step number to ensure correct progress tracking
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep5Submit = async (stepData: Step5Data) => {
    try {
      const updatedFormData = {
        ...formData,
        ...stepData,
      };

      setFormData(updatedFormData);
      // Save with the next step number to ensure correct progress tracking
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep6Submit = async (stepData: Step6Data) => {
    try {
      const updatedFormData = {
        ...formData,
        ...stepData,
      };

      setFormData(updatedFormData);
      // Save with the next step number to ensure correct progress tracking
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep7Submit = async (stepData: Step7Data) => {
    try {
      const updatedFormData = {
        ...formData,
        ...stepData,
      };

      setFormData(updatedFormData);
      // Save with the next step number to ensure correct progress tracking
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep8Submit = async (stepData: Step8Data) => {
    try {
      const updatedFormData = {
        ...formData,
        ...stepData,
      };

      setFormData(updatedFormData);
      // Save with the next step number to ensure correct progress tracking
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep9Submit = async (stepData: Step9Data) => {
    try {
      const updatedFormData = {
        ...formData,
        ...stepData,
      };

      setFormData(updatedFormData);
      await saveFormData(updatedFormData, agentId, step, uploadedFiles);

      // Send WhatsApp message to user's phone after form completion
      if (updatedFormData.phone) {
        try {
          console.log('Sending WhatsApp message to:', updatedFormData.phone);
          const response = await fetch('/api/send-whatsapp', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              phone: updatedFormData.phone
            })
          });

          const result = await response.json();

          if (!result.success) {
            console.error('Failed to send WhatsApp message to user:', result.error);
            // Don't block the flow if message sending fails
          } else {
            console.log('WhatsApp message sent to user successfully:', result);
          }
        } catch (error) {
          console.error('Error sending WhatsApp message to user:', error);
          // Don't block the flow if message sending fails
        }
      } else {
        console.log('No phone number found, skipping WhatsApp message');
      }

      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  // Determine the current step title
  const getStepTitle = () => {
    switch (step) {
      case 1:
        return 'Добро пожаловать';
      case 2:
        return 'Загрузка документов';
      case 3:
        return 'Личная информация';
      case 4:
        return 'Цель поездки';
      case 5:
        return 'История визы';
      case 6:
        return 'Контактная информация';
      case 7:
        return 'Информация о семье';
      case 8:
        return 'Образование и работа';
      case 9:
        return 'История поездок';
      default:
        return 'Анкета на визу';
    }
  };

  // Determine which form component to render based on the current step
  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <Step1_Welcome
            initialValues={{ country: formData.country }}
            onSubmit={handleStep1Submit}
          />
        );
      case 2:
        return (
          <Step2_DocumentUpload
            initialValues={{
              surname: formData.surname,
              name: formData.name,
              dateOfBirth: formData.dateOfBirth,
              citizenship: formData.citizenship,
              passportNumber: formData.passportNumber,
              passportIssueDate: formData.passportIssueDate,
              passportExpiryDate: formData.passportExpiryDate,
              iin: formData.iin,
              idNumber: formData.idNumber,
            }}
            onSubmit={handleStep2Submit}
            uploadedFiles={uploadedFiles}
            setUploadedFiles={setUploadedFiles}
          />
        );
      case 3:
        return (
          <Step3_PersonalInfo
            initialValues={{
              fullNameCyrillic: formData.fullNameCyrillic,
              hasOtherNames: formData.hasOtherNames,
              otherNames: formData.otherNames,
              gender: formData.gender,
              maritalStatus: formData.maritalStatus,
              cityOfBirth: formData.cityOfBirth,
              countryOfBirth: formData.countryOfBirth,
              hasOtherCitizenship: formData.hasOtherCitizenship,
              otherCitizenship: formData.otherCitizenship,
              isPermanentResidentOtherCountry: formData.isPermanentResidentOtherCountry,
              permanentResidenceCountry: formData.permanentResidenceCountry,
              nationality: formData.nationality,
              hasSSN: formData.hasSSN,
              ssn: formData.ssn,
              hasTaxpayerId: formData.hasTaxpayerId,
              taxpayerId: formData.taxpayerId,
            }}
            onSubmit={handleStep3Submit}
          />
        );
      case 4:
        return (
          <Step4_TravelPurpose
            initialValues={{
              travelPurpose: formData.travelPurpose || '',
              travelWithOthers: formData.travelWithOthers || false,
              travelAsGroup: formData.travelAsGroup,
              groupName: formData.groupName,
              companions: formData.companions || [],
            }}
            onSubmit={handleStep4Submit}
          />
        );
      case 5:
        return (
          <Step5_VisaHistory
            initialValues={{
              hasBeenToUSA: formData.hasBeenToUSA || false,
              hasUSVisa: formData.hasUSVisa || false,
              lastVisaDate: formData.lastVisaDate,
              visaNumber: formData.visaNumber,
              isSameVisaType: formData.isSameVisaType,
              isSameCountry: formData.isSameCountry,
              hasVisaRejections: formData.hasVisaRejections || false,
              rejectionVisaType: formData.rejectionVisaType,
              rejectionDate: formData.rejectionDate,
            }}
            onSubmit={handleStep5Submit}
          />
        );
      case 6:
        return (
          <Step6_ContactInfo
            initialValues={{
              address: formData.address || '',
              city: formData.city || '',
              country: formData.country || '',
              zipCode: formData.zipCode || '',
              phone: formData.phone || '',
              email: formData.email || '',
              socialMediaLinks: formData.socialMediaLinks || [],
            }}
            onSubmit={handleStep6Submit}
          />
        );
      case 7:
        return (
          <Step7_FamilyInfo
            initialValues={{
              fatherSurname: formData.fatherSurname || '',
              fatherName: formData.fatherName || '',
              fatherDateOfBirth: formData.fatherDateOfBirth,
              isFatherDateOfBirthUnknown: formData.isFatherDateOfBirthUnknown || false,
              isFatherInUSA: formData.isFatherInUSA || false,
              fatherUSAReason: formData.fatherUSAReason,
              motherSurname: formData.motherSurname || '',
              motherName: formData.motherName || '',
              motherDateOfBirth: formData.motherDateOfBirth,
              isMotherDateOfBirthUnknown: formData.isMotherDateOfBirthUnknown || false,
              isMotherInUSA: formData.isMotherInUSA || false,
              motherUSAReason: formData.motherUSAReason,
              hasRelativesInUSA: formData.hasRelativesInUSA || false,
              relatives: formData.relatives || [],
            }}
            onSubmit={handleStep7Submit}
          />
        );
      case 8:
        return (
          <Step8_EducationWork
            initialValues={{
              occupation: formData.occupation || '',
              // Employment fields
              companyName: formData.companyName,
              position: formData.position,
              workAddress: formData.workAddress,
              workPhone: formData.workPhone,
              workExperience: formData.workExperience,
              income: formData.income,
              // Student fields
              universityName: formData.universityName,
              universityAddress: formData.universityAddress,
              faculty: formData.faculty,
              startDate: formData.startDate,
              endDate: formData.endDate,
              // Business fields
              businessType: formData.businessType,
              businessName: formData.businessName,
              businessRegistrationType: formData.businessRegistrationType,
              businessRegistrationNumber: formData.businessRegistrationNumber,
              businessRegistrationDate: formData.businessRegistrationDate,
              businessActivity: formData.businessActivity,
              monthlyBusinessIncome: formData.monthlyBusinessIncome,
              hasEmployees: formData.hasEmployees || false,
              employeeCount: formData.employeeCount,
              businessStatus: formData.businessStatus,
              businessAddress: formData.businessAddress,
              businessWebsite: formData.businessWebsite,
              hasInternationalClients: formData.hasInternationalClients || false,
              hasPermanentContracts: formData.hasPermanentContracts || false,
              paysTaxes: formData.paysTaxes || false,
              businessExperienceYears: formData.businessExperienceYears,
              hasBankStatements: formData.hasBankStatements || false,
              yearlyIncome: formData.yearlyIncome,
              hasOffice: formData.hasOffice || false,
              officeAddress: formData.officeAddress,
            }}
            onSubmit={handleStep8Submit}
          />
        );
      case 9:
        return (
          <Step9_TravelHistory
            initialValues={{
              visitedCountries: formData.visitedCountries || [],
            }}
            onSubmit={handleStep9Submit}
          />
        );
      default:
        return (
          <div className="text-center p-8">
            <h2 className="text-xl font-semibold">Шаг в разработке</h2>
            <p className="text-gray-600 mt-2">Этот раздел анкеты пока находится в разработке.</p>
            <button
              className="btn-primary mt-4"
              onClick={handleNext}
            >
              Продолжить
            </button>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        <p className="ml-3 text-gray-700">Загрузка данных...</p>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Анкета на визу - {getStepTitle()}</title>
        <meta name="description" content="Заполните анкету для получения визы" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="container mx-auto px-4 py-8">
        {errorMessage && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {errorMessage}
          </div>
        )}

        <StepWrapper
          title={getStepTitle()}
          step={step}
          totalSteps={totalSteps}
          onNext={handleNext}
          onPrev={handlePrev}
          canGoNext={true} // This should be determined by form validation in a real app
        >
          {renderStepContent()}
        </StepWrapper>
      </main>
    </>
  );
}